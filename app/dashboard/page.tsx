'use client';

import { useAuth } from '@/hooks/useAuth';
import { useCredits } from '@/hooks/useCredits';
import ImageGenerator from '@/components/ImageGenerator';

export default function DashboardPage() {
  const { user, loading, logout } = useAuth();
  const { balance, loading: balanceLoading, packages, purchaseCredits } = useCredits();

  if (loading) {
    return (
      <main className="min-h-dvh flex items-center justify-center">Загрузка...</main>
    );
  }

  if (!user) {
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
    return null;
  }

  const handleBuy = async (packageId: string) => {
    const res = await purchaseCredits(packageId);
    if (res?.paymentUrl) {
      window.location.href = res.paymentUrl;
    }
  };

  return (
    <main className="p-6 max-w-4xl mx-auto space-y-8">
      <header className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Привет, {user.name || user.email}</h1>
          <div className="text-gray-600 text-sm">ID: {user.id} · Роль: {user.role}</div>
        </div>
        <button onClick={() => logout()} className="text-red-600 underline">Выйти</button>
      </header>

      <section className="space-y-2">
        <h2 className="text-xl font-semibold">Баланс</h2>
        <div>
          {balanceLoading ? 'Загрузка баланса...' : <span>Кредиты: {balance}</span>}
        </div>
      </section>

      <section className="space-y-3">
        <h2 className="text-xl font-semibold">Пакеты кредитов</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {packages.map((p) => (
            <div key={p.id} className="border rounded p-4 space-y-1">
              <div className="font-medium">{p.name}</div>
              <div className="text-sm text-gray-700">{p.description}</div>
              <div>
                <span className="font-semibold">{p.credits}</span> кредитов · {p.price} {p.currency}
              </div>
              {p.bonus > 0 && (
                <div className="text-green-600 text-sm">Бонус: +{p.bonus}</div>
              )}
              <button
                className="mt-2 bg-blue-600 text-white px-3 py-1 rounded"
                onClick={() => handleBuy(p.id)}
              >
                Купить
              </button>
            </div>
          ))}
        </div>
      </section>

      <section className="space-y-3">
        <h2 className="text-xl font-semibold">Генерация изображений</h2>
        <ImageGenerator />
      </section>
    </main>
  );
}
