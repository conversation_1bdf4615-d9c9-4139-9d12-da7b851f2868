'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

export default function VerifyPageWithLocale() {
  const search = useSearchParams();
  const router = useRouter();
  const { verifyMagicLink } = useAuth();
  const [message, setMessage] = useState('Проверяем ссылку...');

  useEffect(() => {
    const token = search.get('token');
    if (!token) {
      setMessage('Токен не найден');
      return;
    }
    (async () => {
      try {
        const res = await verifyMagicLink(token);
        if (res.success) {
          setMessage('Успешный вход. Перенаправляем...');
          setTimeout(() => router.replace('/dashboard'), 800);
        } else {
          setMessage('Не удалось подтвердить ссылку');
        }
      } catch {
        setMessage('Ошибка при подтверждении ссылки');
      }
    })();
  }, [router, search, verifyMagicLink]);

  return (
    <main className="min-h-dvh flex items-center justify-center p-6">
      <div className="text-center text-lg">{message}</div>
    </main>
  );
}
