"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, Z<PERSON>, Clock, DollarSign, <PERSON>rkles, TrendingUp, Shield } from "lucide-react"

export default function HomePage() {
  const [isDragging, setIsDragging] = useState(false)

  return (
    <div className="min-h-screen bg-background text-foreground">
      <header className="border-b border-border/50">
        <div className="container mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold tracking-tight">MarketPics</div>
            <nav className="flex items-center gap-8">
              <a href="#pricing" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Цены
              </a>
              <a href="#api" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                API
              </a>
              <a href="#blog" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Блог
              </a>
              <button className="bg-foreground text-background px-5 py-2.5 rounded-full text-sm font-medium hover:opacity-90 transition-opacity">
                Начать
              </button>
            </nav>
          </div>
        </div>
      </header>

      <section className="pt-32 pb-20">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto text-center">
            <div className="inline-flex items-center justify-center mb-12">
              <div className="w-12 h-12 relative">
                <svg viewBox="0 0 40 40" fill="none" className="w-full h-full">
                  <rect width="40" height="40" rx="8" fill="currentColor" className="text-primary/10" />
                  <path d="M20 10L30 20L20 30L10 20L20 10Z" fill="currentColor" className="text-primary" />
                </svg>
              </div>
            </div>

            <h1 className="text-7xl lg:text-8xl font-bold tracking-tight leading-none mb-8">
              <span className="inline-block bg-gradient-to-r from-primary via-chart-2 to-chart-4 bg-clip-text text-transparent px-4 py-2">
                Превращаем фото
                <br />в продажи
              </span>
            </h1>

            <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed">
              Профессиональные фотографии товаров для маркетплейсов за 30 секунд. Без фотографа, студии и оборудования.
            </p>

            <div className="mb-16 space-y-6">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium">
                <span>🎁</span>
                <span>5 генераций бесплатно</span>
              </div>
              <div>
                <button className="bg-foreground text-background px-8 py-4 rounded-full text-base font-semibold hover:opacity-90 transition-opacity">
                  Попробовать бесплатно
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-20">
              <div className="space-y-4">
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img
                    src="/poor-quality-phone-photo-of-sneakers-on-messy-back.jpg"
                    alt="Обувь до"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img src="/simple-sketch-of-cat.jpg" alt="Скетч кота" className="w-full h-full object-cover" />
                </div>
              </div>
              <div className="space-y-4 md:mt-8">
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img
                    src="/professional-sneakers-product-photography-clean-wh.jpg"
                    alt="Обувь после"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img
                    src="/cyberpunk-cat-with-sunglasses-neon-city.jpg"
                    alt="Киберпанк кот"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img src="/basic-coffee-shop-sketch.jpg" alt="Скетч кофейни" className="w-full h-full object-cover" />
                </div>
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img src="/blurry-phone-photo-of-handbag.jpg" alt="Сумка до" className="w-full h-full object-cover" />
                </div>
              </div>
              <div className="space-y-4 md:mt-8">
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img
                    src="/professional-product-photo-.jpg"
                    alt="Продукт после"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="aspect-square rounded-2xl overflow-hidden bg-muted">
                  <img
                    src="/mobile-app-product-photo-.jpg"
                    alt="Мобильное приложение"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>

            <button className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-2 mx-auto group">
              <span className="text-sm">Посмотреть возможности</span>
              <ArrowDown className="w-4 h-4 group-hover:translate-y-1 transition-transform" />
            </button>
          </div>
        </div>
      </section>

      <section className="py-20 border-t border-border/50">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4">Почему выбирают нас</h2>
              <p className="text-muted-foreground text-lg">Всё, что нужно для успешных продаж на маркетплейсах</p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center space-y-3 p-6 rounded-2xl border border-border/50 hover:border-primary/50 transition-colors">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Больше продаж</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Профессиональные фото увеличивают конверсию на 40% и CTR на 60%
                </p>
              </div>
              <div className="text-center space-y-3 p-6 rounded-2xl border border-border/50 hover:border-chart-2/50 transition-colors">
                <div className="w-12 h-12 rounded-full bg-chart-2/10 flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-6 h-6 text-chart-2" />
                </div>
                <h3 className="text-xl font-semibold">Экономия времени</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Генерация за 30 секунд вместо часов фотосъемки и обработки
                </p>
              </div>
              <div className="text-center space-y-3 p-6 rounded-2xl border border-border/50 hover:border-chart-4/50 transition-colors">
                <div className="w-12 h-12 rounded-full bg-chart-4/10 flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-6 h-6 text-chart-4" />
                </div>
                <h3 className="text-xl font-semibold">Без фотографа</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Не нужно тратить деньги на студию, оборудование и специалистов
                </p>
              </div>
              <div className="text-center space-y-3 p-6 rounded-2xl border border-border/50 hover:border-chart-1/50 transition-colors">
                <div className="w-12 h-12 rounded-full bg-chart-1/10 flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-6 h-6 text-chart-1" />
                </div>
                <h3 className="text-xl font-semibold">AI-качество</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Нейросеть создает фото студийного качества с идеальным освещением
                </p>
              </div>
              <div className="text-center space-y-3 p-6 rounded-2xl border border-border/50 hover:border-chart-3/50 transition-colors">
                <div className="w-12 h-12 rounded-full bg-chart-3/10 flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-chart-3" />
                </div>
                <h3 className="text-xl font-semibold">Быстрый старт</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Загрузите фото на телефоне и получите результат за минуту
                </p>
              </div>
              <div className="text-center space-y-3 p-6 rounded-2xl border border-border/50 hover:border-chart-5/50 transition-colors">
                <div className="w-12 h-12 rounded-full bg-chart-5/10 flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-6 h-6 text-chart-5" />
                </div>
                <h3 className="text-xl font-semibold">Безопасность</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Ваши фото защищены и не используются для обучения AI
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 border-t border-border/50 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4">Работает на всех маркетплейсах</h2>
              <p className="text-muted-foreground text-lg">Создавайте профессиональные фото для любой платформы</p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-background rounded-2xl overflow-hidden border border-border/50 hover:shadow-lg transition-shadow">
                <div className="aspect-square bg-muted relative">
                  <img
                    src="/professional-sneakers-product-photography-clean-wh.jpg"
                    alt="Wildberries"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4 bg-background/90 backdrop-blur-sm px-3 py-1.5 rounded-full text-xs font-medium">
                    Wildberries
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="font-semibold mb-2">Кроссовки Nike</h3>
                  <p className="text-sm text-muted-foreground mb-4">Спортивная обувь</p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold">2 490 ₽</span>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <span>⭐</span>
                      <span>4.8</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-background rounded-2xl overflow-hidden border border-border/50 hover:shadow-lg transition-shadow">
                <div className="aspect-square bg-muted relative">
                  <img src="/professional-product-photo-.jpg" alt="Ozon" className="w-full h-full object-cover" />
                  <div className="absolute top-4 left-4 bg-background/90 backdrop-blur-sm px-3 py-1.5 rounded-full text-xs font-medium">
                    Ozon
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="font-semibold mb-2">Умные часы</h3>
                  <p className="text-sm text-muted-foreground mb-4">Электроника</p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold">5 990 ₽</span>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <span>⭐</span>
                      <span>4.9</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-background rounded-2xl overflow-hidden border border-border/50 hover:shadow-lg transition-shadow">
                <div className="aspect-square bg-muted relative">
                  <img
                    src="/professional-product-photography-artistic.jpg"
                    alt="Avito"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4 bg-background/90 backdrop-blur-sm px-3 py-1.5 rounded-full text-xs font-medium">
                    Avito
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="font-semibold mb-2">Дизайнерская сумка</h3>
                  <p className="text-sm text-muted-foreground mb-4">Аксессуары</p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold">3 500 ₽</span>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <span>⭐</span>
                      <span>5.0</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 border-t border-border/50">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4">Как это работает</h2>
              <p className="text-muted-foreground text-lg">Три простых шага до идеального фото</p>
            </div>
            <div className="space-y-12">
              <div className="flex gap-8 items-start">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-bold text-lg">
                  1
                </div>
                <div>
                  <h3 className="text-2xl font-semibold mb-3">Загрузите фото</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Сделайте снимок товара на телефон или загрузите существующее фото. Качество исходника не важно — AI
                    улучшит его.
                  </p>
                </div>
              </div>
              <div className="flex gap-8 items-start">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-chart-2 text-white flex items-center justify-center font-bold text-lg">
                  2
                </div>
                <div>
                  <h3 className="text-2xl font-semibold mb-3">AI обработает</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Нейросеть автоматически улучшит освещение, фон, композицию и создаст профессиональное фото за 30
                    секунд.
                  </p>
                </div>
              </div>
              <div className="flex gap-8 items-start">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-chart-4 text-white flex items-center justify-center font-bold text-lg">
                  3
                </div>
                <div>
                  <h3 className="text-2xl font-semibold mb-3">Публикуйте и продавайте</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Скачайте готовое фото и загрузите на маркетплейс. Наблюдайте, как растут продажи с профессиональными
                    фотографиями.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 border-t border-border/50 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4">Что говорят продавцы</h2>
              <p className="text-muted-foreground text-lg">Реальные отзывы от наших пользователей</p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-background rounded-2xl p-8 border border-border/50">
                <div className="flex items-center gap-1 mb-4 text-amber-500">
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                </div>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  "Продажи выросли на 45% после того, как я начал использовать MarketPics. Фото действительно выглядят
                  профессионально!"
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center font-semibold text-primary">
                    АС
                  </div>
                  <div>
                    <div className="font-semibold text-sm">Алексей Смирнов</div>
                    <div className="text-xs text-muted-foreground">Продавец на Wildberries</div>
                  </div>
                </div>
              </div>
              <div className="bg-background rounded-2xl p-8 border border-border/50">
                <div className="flex items-center gap-1 mb-4 text-amber-500">
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                </div>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  "Раньше тратила по 3 часа на фотосессию каждого товара. Теперь делаю это за минуты. Экономия времени
                  огромная!"
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-chart-2/10 flex items-center justify-center font-semibold text-chart-2">
                    МК
                  </div>
                  <div>
                    <div className="font-semibold text-sm">Мария Козлова</div>
                    <div className="text-xs text-muted-foreground">Владелец магазина на Ozon</div>
                  </div>
                </div>
              </div>
              <div className="bg-background rounded-2xl p-8 border border-border/50">
                <div className="flex items-center gap-1 mb-4 text-amber-500">
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                  <span>⭐</span>
                </div>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  "Качество фото превзошло ожидания. Клиенты думают, что я нанял профессионального фотографа.
                  Рекомендую!"
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-chart-4/10 flex items-center justify-center font-semibold text-chart-4">
                    ДП
                  </div>
                  <div>
                    <div className="font-semibold text-sm">Дмитрий Петров</div>
                    <div className="text-xs text-muted-foreground">Предприниматель</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 border-t border-border/50">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-5xl font-bold mb-6">Начните продавать больше уже сегодня</h2>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Получите 5 бесплатных генераций и убедитесь в качестве сами
            </p>
            <button className="bg-foreground text-background px-10 py-5 rounded-full text-lg font-semibold hover:opacity-90 transition-opacity">
              Попробовать бесплатно
            </button>
            <p className="text-sm text-muted-foreground mt-4">Кредитная карта не требуется</p>
          </div>
        </div>
      </section>

      <footer className="border-t border-border/50 py-12">
        <div className="container mx-auto px-6">
          <div className="max-w-5xl mx-auto">
            <div className="grid md:grid-cols-4 gap-8 mb-8">
              <div>
                <div className="text-xl font-bold mb-4">MarketPics</div>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Профессиональные фото для маркетплейсов за минуты
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-3 text-sm">Продукт</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      Цены
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      API
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      Примеры
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3 text-sm">Компания</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      О нас
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      Блог
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      Контакты
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3 text-sm">Поддержка</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      Помощь
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      Документация
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-foreground transition-colors">
                      Статус
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div className="pt-8 border-t border-border/50 flex flex-col md:flex-row justify-between items-center gap-4 text-sm text-muted-foreground">
              <p>© 2025 MarketPics. Все права защищены.</p>
              <div className="flex gap-6">
                <a href="#" className="hover:text-foreground transition-colors">
                  Политика конфиденциальности
                </a>
                <a href="#" className="hover:text-foreground transition-colors">
                  Условия использования
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
