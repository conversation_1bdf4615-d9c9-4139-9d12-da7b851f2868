# SAIA Backend API - Complete Documentation

## Overview

Complete API documentation for the SAIA image generation platform. This API provides authentication, image generation (standard and GPT-4 Vision), credit management, and subscription services.

**Base URL**: `https://api.saia.im` (Production) | `http://localhost:3001` (Development)

## Quick Start

### Authentication Flow
1. Send magic link → `POST /api/auth/send-magic-link`
2. User clicks link → `POST /api/auth/verify-magic-link`
3. Get JWT token → Use in `Authorization: Bearer <token>` header
4. Access protected endpoints

### Image Generation Flow
1. Check credits → `GET /api/user/balance`
2. Generate image → `POST /api/images/generate` or `POST /api/images/gpt-vision`
3. Check status → `GET /api/images/status/:id`
4. Download results when completed

---

## Table of Contents

1. [Authentication](#authentication)
2. [User Management](#user-management)
3. [Image Generation](#image-generation)
4. [Credit System](#credit-system)
5. [Subscription Management](#subscription-management)
6. [Admin Panel](#admin-panel)
7. [Webhooks](#webhooks)
8. [Next.js Integration](#nextjs-integration)
9. [TypeScript Interfaces](#typescript-interfaces)
10. [Error Handling](#error-handling)

---

## Authentication

### Magic Link Authentication

#### Send Magic Link
```http
POST /api/auth/send-magic-link
Content-Type: application/json

{
  "email": "<EMAIL>",
  "language": "ru", // optional: ru|en
  "type": "login" // optional: login|register
}
```

**Response:**
```json
{
  "success": true,
  "message": "Magic link sent successfully",
  "expires": "2024-10-08T15:00:00Z"
}
```

#### Verify Magic Link
```http
POST /api/auth/verify-magic-link
Content-Type: application/json

{
  "token": "magic-link-token-from-email"
}
```

**Response:**
```json
{
  "success": true,
  "token": "jwt-token-here",
  "user": {
    "id": 123,
    "email": "<EMAIL>",
    "name": "User Name",
    "avatar": "https://cloudinary.com/avatar.jpg",
    "credits": 50,
    "role": "user",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### User Profile

#### Get Current User
```http
GET /api/auth/me
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": 123,
    "email": "<EMAIL>",
    "name": "User Name",
    "avatar": "https://cloudinary.com/avatar.jpg",
    "credits": 50,
    "role": "user",
    "phone": "+1234567890",
    "phoneVerified": true,
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

#### Update Profile
```http
PUT /api/auth/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "New Name",
  "email": "<EMAIL>"
}
```

#### Upload Avatar
```http
POST /api/auth/avatar
Authorization: Bearer <token>
Content-Type: multipart/form-data

avatar: <image-file>
```

#### Delete Avatar
```http
DELETE /api/auth/avatar
Authorization: Bearer <token>
```

### Phone Verification

#### Send OTP
```http
POST /api/auth/send-otp
Content-Type: application/json

{
  "phone": "+1234567890",
  "language": "ru"
}
```

#### Verify OTP
```http
POST /api/auth/verify-otp
Content-Type: application/json

{
  "phone": "+1234567890",
  "code": "123456"
}
```

### Logout
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

---

## User Management

### Credit Balance
```http
GET /api/user/balance
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "balance": 150,
  "userId": 123
}
```

### Check Sufficient Credits
```http
GET /api/user/credits/check/15
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "sufficient": true,
  "current": 150,
  "required": 15,
  "remaining": 135
}
```

### User Statistics
```http
GET /api/auth/stats
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "stats": {
    "totalGenerations": 45,
    "creditsSpent": 200,
    "averageCreditsPerGeneration": 4.4,
    "favoriteModel": "nano-banana",
    "generationsThisMonth": 12,
    "joinDate": "2024-01-01T00:00:00Z"
  }
}
```

---

## Image Generation

### Standard Generation (Nano-Banana)

#### Generate Image
```http
POST /api/images/generate
Authorization: Bearer <token>
Content-Type: application/json

{
  "prompt": "A beautiful sunset over mountains",
  "images": [
    {
      "url": "https://example.com/input.jpg"
    }
  ],
  "options": {
    "aspectRatio": "1:1",
    "mask": "https://example.com/mask.png"
  }
}
```

**Response:**
```json
{
  "success": true,
  "generationId": "gen123",
  "status": "queued",
  "creditCost": 3,
  "estimatedTime": "30-90 seconds",
  "message": "Image generation has been queued."
}
```

### GPT-4 Vision Generation

#### Generate with GPT-4 Vision
```http
POST /api/images/gpt-vision
Authorization: Bearer <token>
Content-Type: application/json

{
  "prompt": "Add floral pattern to the vase",
  "inputImages": [
    {
      "url": "https://example.com/vase.jpg"
    },
    {
      "url": "https://example.com/pattern.jpg"
    }
  ],
  "openaiApiKey": "sk-your-openai-key",
  "options": {
    "quality": "high",
    "aspectRatio": "1:1",
    "background": "transparent",
    "numberOfImages": 2,
    "outputFormat": "png"
  }
}
```

**Response:**
```json
{
  "success": true,
  "generationId": "gpt456",
  "status": "queued",
  "model": "gpt-4-vision",
  "creditCost": 30,
  "numberOfImages": 2,
  "estimatedTime": "60-180 seconds",
  "message": "GPT-4 Vision generation has been queued."
}
```

### Check Generation Status
```http
GET /api/images/status/gen123
Authorization: Bearer <token>
```

**Response (Completed):**
```json
{
  "success": true,
  "status": "completed",
  "generationId": "gen123",
  "model": "nano-banana",
  "prompt": "A beautiful sunset over mountains",
  "creditCost": 3,
  "results": [
    {
      "url": "https://replicate.delivery/.../output.jpg",
      "filename": "generated_gen123.jpg"
    }
  ],
  "createdAt": "2024-10-08T14:30:00Z",
  "completedAt": "2024-10-08T14:31:30Z"
}
```

**Response (Processing):**
```json
{
  "success": true,
  "status": "processing",
  "generationId": "gen123",
  "estimatedCompletion": "2024-10-08T14:32:00Z",
  "progress": {
    "stage": "generating",
    "percentage": 65
  }
}
```

**Response (Failed):**
```json
{
  "success": false,
  "status": "failed",
  "generationId": "gen123",
  "error": "Generation failed due to content policy violation"
}
```

### Cancel Generation
```http
DELETE /api/images/cancel/gen123
Authorization: Bearer <token>
```

### User's Generated Images
```http
GET /api/images/user/123?page=1&limit=20
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "images": [
    {
      "id": "gen123",
      "prompt": "A sunset",
      "status": "completed",
      "model": "nano-banana",
      "creditCost": 3,
      "results": ["https://..."],
      "createdAt": "2024-10-08T14:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "pages": 3
  }
}
```

### Delete Generated Image
```http
DELETE /api/images/gen123
Authorization: Bearer <token>
```

### Queue Statistics
```http
GET /api/images/queue/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "queue": {
    "waiting": 5,
    "active": 2,
    "completed": 1250,
    "failed": 23,
    "totalProcessed": 1273
  },
  "estimatedWaitTime": "45 seconds"
}
```

---

## Credit System

### Available Packages
```http
GET /api/credits/packages
```

**Response:**
```json
{
  "success": true,
  "packages": [
    {
      "id": "small",
      "name": "Базовый пакет",
      "credits": 10,
      "price": 200,
      "currency": "RUB",
      "description": "Для начинающих",
      "popular": false,
      "bonus": 0
    },
    {
      "id": "medium",
      "name": "Средний пакет",
      "credits": 50,
      "price": 850,
      "currency": "RUB",
      "description": "Оптимальный выбор",
      "popular": true,
      "bonus": 5
    },
    {
      "id": "large",
      "name": "Премиум пакет",
      "credits": 100,
      "price": 1500,
      "currency": "RUB",
      "description": "Для профессионалов",
      "popular": false,
      "bonus": 15
    }
  ]
}
```

### Purchase Credits
```http
POST /api/credits/buy
Authorization: Bearer <token>
Content-Type: application/json

{
  "packageId": "medium"
}
```

**Response:**
```json
{
  "success": true,
  "orderId": 1759943631848,
  "package": {
    "id": "medium",
    "name": "Средний пакет",
    "credits": 50,
    "price": 850
  },
  "paymentUrl": "https://auth.robokassa.ru/Merchant/Index.aspx?...",
  "expiresAt": "2024-10-09T14:30:00Z"
}
```

### Check Payment Status
```http
GET /api/credits/payment/status/1759943631848
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "orderId": 1759943631848,
  "status": "completed",
  "package": {
    "id": "medium",
    "credits": 50,
    "price": 850
  },
  "creditsAdded": 55,
  "completedAt": "2024-10-08T14:35:00Z"
}
```

---

## Subscription Management

### Available Plans
```http
GET /api/subscription/plans
```

**Response:**
```json
{
  "success": true,
  "plans": [
    {
      "id": "starter",
      "name": "Starter",
      "price": 990,
      "currency": "RUB",
      "interval": "month",
      "credits": 100,
      "features": ["Standard generation", "Email support"]
    },
    {
      "id": "pro",
      "name": "Pro",
      "price": 1990,
      "currency": "RUB",
      "interval": "month",
      "credits": 250,
      "features": ["All generation models", "Priority support", "GPT-4 Vision"]
    }
  ]
}
```

### Create Checkout Session
```http
POST /api/subscription/create-checkout
Authorization: Bearer <token>
Content-Type: application/json

{
  "planId": "pro",
  "successUrl": "https://yourapp.com/success",
  "cancelUrl": "https://yourapp.com/cancel"
}
```

### Subscription Status
```http
GET /api/subscription/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "subscription": {
    "id": "sub_123",
    "planId": "pro",
    "status": "active",
    "currentPeriodStart": "2024-10-01T00:00:00Z",
    "currentPeriodEnd": "2024-11-01T00:00:00Z",
    "cancelAtPeriodEnd": false
  },
  "usage": {
    "creditsUsed": 45,
    "creditsTotal": 250,
    "resetDate": "2024-11-01T00:00:00Z"
  }
}
```

### Cancel Subscription
```http
POST /api/subscription/cancel
Authorization: Bearer <token>
```

### Billing History
```http
GET /api/subscription/billing-history
Authorization: Bearer <token>
```

---

## Admin Panel

### Admin Login
```http
POST /api/admin/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin-password"
}
```

### User Management

#### List Users
```http
GET /api/admin/users?page=1&limit=50&search=<EMAIL>
Authorization: Bearer <admin-token>
```

#### Get User Details
```http
GET /api/admin/users/123
Authorization: Bearer <admin-token>
```

#### Update User Credits
```http
PUT /api/admin/users/123/credits
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "amount": 100,
  "operation": "add", // add|subtract|set
  "reason": "Bonus credits for feedback"
}
```

#### Delete User
```http
DELETE /api/admin/users/123
Authorization: Bearer <admin-token>
```

### System Analytics
```http
GET /api/admin/analytics
Authorization: Bearer <admin-token>
```

**Response:**
```json
{
  "success": true,
  "analytics": {
    "users": {
      "total": 1250,
      "newThisMonth": 45,
      "activeThisMonth": 320
    },
    "generations": {
      "total": 15000,
      "thisMonth": 2500,
      "averagePerUser": 12
    },
    "revenue": {
      "thisMonth": 125000,
      "lastMonth": 98000,
      "growth": 27.6
    },
    "topModels": [
      { "model": "nano-banana", "count": 12000 },
      { "model": "gpt-4-vision", "count": 3000 }
    ]
  }
}
```

---

## Webhooks

### Stripe Webhook
```http
POST /api/webhooks/stripe
Stripe-Signature: <stripe-signature>
Content-Type: application/json

{
  "type": "customer.subscription.created",
  "data": { ... }
}
```

### Robokassa Webhooks

#### Result URL (Payment Confirmation)
```http
POST /api/webhooks/robokassa/result
Content-Type: application/x-www-form-urlencoded

OutSum=850.00&InvId=1759943631848&SignatureValue=abc123&Shp_store=saia
```

#### Success URL (User Redirect)
```http
GET /api/webhooks/robokassa/success?OutSum=850.00&InvId=1759943631848&SignatureValue=abc123
```

#### Fail URL (Failed Payment)
```http
GET /api/webhooks/robokassa/fail?OutSum=850.00&InvId=1759943631848
```

---

## Next.js Integration

### Installation

```bash
npm install @better-auth/core @better-auth/next axios
```

### Better Auth Configuration

Create `lib/auth.ts`:

```typescript
import { betterAuth } from "@better-auth/core"
import { nextjsAdapter } from "@better-auth/next"

export const auth = betterAuth({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  adapter: nextjsAdapter(),
  providers: {
    // Custom provider for SAIA magic link auth
    email: {
      async sendMagicLink(email: string) {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/send-magic-link`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, language: 'ru' })
        });
        return response.json();
      }
    }
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  }
})
```

### API Client Setup

Create `lib/api.ts`:

```typescript
import axios from 'axios';

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 30000,
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth-token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth-token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

### Authentication Hook

Create `hooks/useAuth.ts`:

```typescript
import { useState, useEffect } from 'react';
import api from '@/lib/api';
import { User } from '@/types/api';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('auth-token');
      if (!token) {
        setLoading(false);
        return;
      }

      const response = await api.get('/api/auth/me');
      setUser(response.data.user);
    } catch (error) {
      localStorage.removeItem('auth-token');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const sendMagicLink = async (email: string) => {
    const response = await api.post('/api/auth/send-magic-link', {
      email,
      language: 'ru'
    });
    return response.data;
  };

  const verifyMagicLink = async (token: string) => {
    const response = await api.post('/api/auth/verify-magic-link', { token });
    if (response.data.success) {
      localStorage.setItem('auth-token', response.data.token);
      setUser(response.data.user);
    }
    return response.data;
  };

  const logout = async () => {
    try {
      await api.post('/api/auth/logout');
    } finally {
      localStorage.removeItem('auth-token');
      setUser(null);
    }
  };

  return {
    user,
    loading,
    sendMagicLink,
    verifyMagicLink,
    logout,
    checkAuth
  };
}
```

### Image Generation Hook

Create `hooks/useImageGeneration.ts`:

```typescript
import { useState } from 'react';
import api from '@/lib/api';
import { GenerationRequest, GenerationResponse, GenerationStatus } from '@/types/api';

export function useImageGeneration() {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<GenerationStatus | null>(null);

  const generateImage = async (request: GenerationRequest): Promise<string> => {
    setLoading(true);
    try {
      const response = await api.post('/api/images/generate', request);
      const generationId = response.data.generationId;

      // Start polling for status
      pollStatus(generationId);

      return generationId;
    } finally {
      setLoading(false);
    }
  };

  const generateWithGPTVision = async (request: GPTVisionRequest): Promise<string> => {
    setLoading(true);
    try {
      const response = await api.post('/api/images/gpt-vision', request);
      const generationId = response.data.generationId;

      pollStatus(generationId);

      return generationId;
    } finally {
      setLoading(false);
    }
  };

  const pollStatus = async (generationId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await api.get(`/api/images/status/${generationId}`);
        const statusData = response.data;

        setStatus(statusData);

        if (statusData.status === 'completed' || statusData.status === 'failed') {
          clearInterval(interval);
        }
      } catch (error) {
        console.error('Status polling error:', error);
        clearInterval(interval);
      }
    }, 3000); // Poll every 3 seconds
  };

  const cancelGeneration = async (generationId: string) => {
    await api.delete(`/api/images/cancel/${generationId}`);
    setStatus(null);
  };

  return {
    loading,
    status,
    generateImage,
    generateWithGPTVision,
    cancelGeneration,
    clearStatus: () => setStatus(null)
  };
}
```

### Credit Management Hook

Create `hooks/useCredits.ts`:

```typescript
import { useState, useEffect } from 'react';
import api from '@/lib/api';

export function useCredits() {
  const [balance, setBalance] = useState(0);
  const [packages, setPackages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadBalance();
    loadPackages();
  }, []);

  const loadBalance = async () => {
    try {
      const response = await api.get('/api/user/balance');
      setBalance(response.data.balance);
    } catch (error) {
      console.error('Failed to load balance:', error);
    }
  };

  const loadPackages = async () => {
    try {
      const response = await api.get('/api/credits/packages');
      setPackages(response.data.packages);
    } catch (error) {
      console.error('Failed to load packages:', error);
    } finally {
      setLoading(false);
    }
  };

  const purchaseCredits = async (packageId: string) => {
    const response = await api.post('/api/credits/buy', { packageId });
    return response.data;
  };

  const checkPaymentStatus = async (orderId: string) => {
    const response = await api.get(`/api/credits/payment/status/${orderId}`);
    return response.data;
  };

  return {
    balance,
    packages,
    loading,
    loadBalance,
    purchaseCredits,
    checkPaymentStatus
  };
}
```

### Example Components

#### Login Component
```tsx
'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [sent, setSent] = useState(false);
  const { sendMagicLink } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await sendMagicLink(email);
      setSent(true);
    } catch (error) {
      console.error('Failed to send magic link:', error);
    }
  };

  if (sent) {
    return (
      <div className="text-center">
        <h2>Check your email</h2>
        <p>We've sent a magic link to {email}</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label>Email</label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="w-full p-2 border rounded"
        />
      </div>
      <button
        type="submit"
        className="w-full bg-blue-500 text-white p-2 rounded"
      >
        Send Magic Link
      </button>
    </form>
  );
}
```

#### Image Generator Component
```tsx
'use client';

import { useState } from 'react';
import { useImageGeneration } from '@/hooks/useImageGeneration';
import { useCredits } from '@/hooks/useCredits';

export default function ImageGenerator() {
  const [prompt, setPrompt] = useState('');
  const [useGPTVision, setUseGPTVision] = useState(false);
  const [openaiApiKey, setOpenaiApiKey] = useState('');
  const { generateImage, generateWithGPTVision, loading, status } = useImageGeneration();
  const { balance } = useCredits();

  const handleGenerate = async () => {
    const requiredCredits = useGPTVision ? 15 : 3;

    if (balance < requiredCredits) {
      alert(`Insufficient credits. Required: ${requiredCredits}, Available: ${balance}`);
      return;
    }

    if (useGPTVision) {
      if (!openaiApiKey) {
        alert('OpenAI API key is required for GPT-4 Vision');
        return;
      }

      await generateWithGPTVision({
        prompt,
        openaiApiKey,
        options: {
          quality: 'high',
          numberOfImages: 1
        }
      });
    } else {
      await generateImage({
        prompt,
        options: {
          aspectRatio: '1:1'
        }
      });
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label>Prompt</label>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="w-full p-2 border rounded"
          rows={3}
        />
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={useGPTVision}
          onChange={(e) => setUseGPTVision(e.target.checked)}
        />
        <label>Use GPT-4 Vision (15 credits)</label>
      </div>

      {useGPTVision && (
        <div>
          <label>OpenAI API Key</label>
          <input
            type="password"
            value={openaiApiKey}
            onChange={(e) => setOpenaiApiKey(e.target.value)}
            className="w-full p-2 border rounded"
          />
        </div>
      )}

      <div className="text-sm text-gray-600">
        Credits available: {balance}
      </div>

      <button
        onClick={handleGenerate}
        disabled={loading || !prompt}
        className="w-full bg-blue-500 text-white p-2 rounded disabled:opacity-50"
      >
        {loading ? 'Generating...' : `Generate (${useGPTVision ? 15 : 3} credits)`}
      </button>

      {status && (
        <div className="mt-4">
          <div>Status: {status.status}</div>
          {status.status === 'processing' && status.progress && (
            <div>Progress: {status.progress.percentage}%</div>
          )}
          {status.status === 'completed' && status.results && (
            <div className="mt-2">
              {status.results.map((result, index) => (
                <img key={index} src={result.url} alt="Generated" className="max-w-full" />
              ))}
            </div>
          )}
          {status.status === 'failed' && (
            <div className="text-red-500">Error: {status.error}</div>
          )}
        </div>
      )}
    </div>
  );
}
```

---

## TypeScript Interfaces

Create `types/api.ts`:

```typescript
// User Types
export interface User {
  id: number;
  email: string;
  name: string;
  avatar?: string;
  credits: number;
  role: 'user' | 'admin';
  phone?: string;
  phoneVerified: boolean;
  createdAt: string;
}

export interface AuthResponse {
  success: boolean;
  token: string;
  user: User;
}

// Image Generation Types
export interface GenerationRequest {
  prompt: string;
  images?: Array<{ url: string }>;
  options?: {
    aspectRatio?: '1:1' | '3:2' | '2:3';
    mask?: string;
  };
}

export interface GPTVisionRequest {
  prompt: string;
  inputImages?: Array<{ url: string }>;
  openaiApiKey: string;
  options?: {
    quality?: 'low' | 'medium' | 'high' | 'auto';
    aspectRatio?: '1:1' | '3:2' | '2:3';
    background?: 'auto' | 'transparent' | 'opaque';
    outputFormat?: 'png' | 'jpeg' | 'webp';
    numberOfImages?: number;
    inputFidelity?: 'low' | 'high';
  };
}

export interface GenerationResponse {
  success: boolean;
  generationId: string;
  status: 'queued';
  model: string;
  creditCost: number;
  numberOfImages?: number;
  estimatedTime: string;
  message: string;
}

export interface GenerationResult {
  url: string;
  filename: string;
}

export interface GenerationStatus {
  success: boolean;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  generationId: string;
  model?: string;
  prompt?: string;
  creditCost?: number;
  results?: GenerationResult[];
  progress?: {
    stage: string;
    percentage: number;
  };
  error?: string;
  createdAt?: string;
  completedAt?: string;
  estimatedCompletion?: string;
}

// Credit Types
export interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  currency: string;
  description: string;
  popular: boolean;
  bonus: number;
}

export interface PurchaseResponse {
  success: boolean;
  orderId: number;
  package: CreditPackage;
  paymentUrl: string;
  expiresAt: string;
}

export interface PaymentStatus {
  success: boolean;
  orderId: number;
  status: 'pending' | 'completed' | 'failed';
  package: CreditPackage;
  creditsAdded?: number;
  completedAt?: string;
}

// Subscription Types
export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  credits: number;
  features: string[];
}

export interface Subscription {
  id: string;
  planId: string;
  status: 'active' | 'canceled' | 'past_due';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
}

export interface SubscriptionUsage {
  creditsUsed: number;
  creditsTotal: number;
  resetDate: string;
}

// Error Types
export interface APIError {
  error: string;
  message?: string;
  code?: string;
}

export interface InsufficientCreditsError extends APIError {
  required: number;
  available: number;
}
```

### Environment Variables

Create `.env.local`:

```bash
NEXT_PUBLIC_API_URL=https://api.saia.im
# For development: http://localhost:3001

NEXT_PUBLIC_APP_URL=https://yourapp.com
```

---

## Error Handling

### Common HTTP Status Codes

| Code | Meaning | Common Causes |
|------|---------|---------------|
| 400 | Bad Request | Invalid parameters, missing required fields |
| 401 | Unauthorized | Invalid or expired JWT token |
| 402 | Payment Required | Insufficient credits for operation |
| 403 | Forbidden | User doesn't have permission |
| 404 | Not Found | Resource doesn't exist |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server-side error |

### Error Response Format

```typescript
interface ErrorResponse {
  error: string;
  message?: string;
  code?: string;
  details?: any;
}
```

### Error Handling Best Practices

1. **Always handle errors gracefully**
2. **Show user-friendly error messages**
3. **Implement retry logic for network errors**
4. **Log errors for debugging**
5. **Handle specific error codes appropriately**

### Example Error Handler

```typescript
export function handleAPIError(error: any): string {
  if (error.response?.data?.error) {
    const apiError = error.response.data;

    switch (error.response.status) {
      case 401:
        return 'Please log in to continue';
      case 402:
        return `Insufficient credits. Required: ${apiError.required}, Available: ${apiError.available}`;
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      default:
        return apiError.message || apiError.error || 'An error occurred';
    }
  }

  if (error.message) {
    return error.message;
  }

  return 'Network error. Please check your connection.';
}
```

---

## Rate Limits

### Current Limits

- **Authentication**: 10 requests per minute per IP
- **Image Generation**: 10 requests per minute per user
- **Credit Operations**: 20 requests per minute per user
- **Concurrent Generations**: 3 per user
- **Queue Size**: 50 pending jobs per user

### Rate Limit Headers

```http
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1609459200
```

---

## Webhooks Security

### Signature Verification

**Stripe Webhooks:**
```typescript
import { verifyWebhookSignature } from 'stripe';

const signature = req.headers['stripe-signature'];
const payload = req.body;
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

const event = verifyWebhookSignature(payload, signature, endpointSecret);
```

**Robokassa Webhooks:**
```typescript
import crypto from 'crypto';

function verifyRobokassaSignature(outSum: string, invId: string, signature: string): boolean {
  const expectedSignature = crypto
    .createHash('md5')
    .update(`${outSum}:${invId}:${process.env.ROBOKASSA_PASS2}`)
    .digest('hex');

  return expectedSignature.toUpperCase() === signature.toUpperCase();
}
```

---

## Testing

### Example Test API Calls

```bash
# Send magic link
curl -X POST https://api.saia.im/api/auth/send-magic-link \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","language":"ru"}'

# Check balance
curl -X GET https://api.saia.im/api/user/balance \
  -H "Authorization: Bearer your-jwt-token"

# Generate image
curl -X POST https://api.saia.im/api/images/generate \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset",
    "options": {"aspectRatio": "1:1"}
  }'
```

---

## Support & Resources

- **API Status**: Monitor uptime and performance
- **Documentation**: This document and endpoint-specific docs
- **Support**: Contact for integration assistance
- **Changelog**: Track API updates and changes

---

*Last updated: October 2024*
*API Version: 1.0*