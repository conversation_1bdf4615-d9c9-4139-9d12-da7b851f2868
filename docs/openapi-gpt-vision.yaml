openapi: 3.0.3
info:
  title: SAIA GPT-4 Vision API
  description: |
    High-quality image generation using OpenAI's GPT-4 Vision model via Replicate.

    This premium service offers advanced image generation capabilities with:
    - Multiple input images support
    - Sophisticated style controls
    - Superior output quality
    - Professional-grade parameters

    ## Authentication
    All endpoints require JWT Bearer token authentication.

    ## Credits System
    GPT-4 Vision is a premium service:
    - Base cost: 15 credits per image
    - Multiple images: Cost multiplied by number of images

    ## Rate Limits
    - 10 requests per minute per user
    - 3 concurrent generations per user
    - 50 pending jobs per user
  version: 1.0.0
  contact:
    name: SAIA API Support
    url: https://saia.im/support
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://saia.im/terms

servers:
  - url: https://api.saia.im/api/images
    description: Production server
  - url: http://localhost:3001/api/images
    description: Development server

security:
  - bearerAuth: []

paths:
  /gpt-vision:
    post:
      summary: Generate images with GPT-4 Vision
      description: |
        Generate high-quality images using OpenAI's GPT-4 Vision model.

        Features:
        - Multiple input images support
        - Advanced quality controls
        - Multiple output formats
        - Transparent backgrounds
        - High-fidelity style transfer

        The generation is processed asynchronously. Use the returned `generationId`
        to check status via the `/status/{generationId}` endpoint.
      operationId: generateGPTVision
      tags:
        - GPT-4 Vision
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GPTVisionRequest'
            examples:
              text-to-image:
                summary: Text to image generation
                value:
                  prompt: "A beautiful sunset over mountains, digital art style"
                  openaiApiKey: "sk-your-openai-api-key"
                  options:
                    quality: "high"
                    aspectRatio: "3:2"
                    numberOfImages: 1
              style-transfer:
                summary: Style transfer with input image
                value:
                  prompt: "Transform this photo into a Van Gogh painting style"
                  inputImages:
                    - url: "https://example.com/photo.jpg"
                  openaiApiKey: "sk-your-openai-api-key"
                  options:
                    quality: "high"
                    inputFidelity: "high"
              pattern-application:
                summary: Apply pattern to object
                value:
                  prompt: "Add the floral pattern to the vase"
                  inputImages:
                    - url: "https://example.com/vase.jpg"
                    - url: "https://example.com/pattern.jpg"
                  openaiApiKey: "sk-your-openai-api-key"
                  options:
                    quality: "high"
                    outputFormat: "png"
                    background: "transparent"
      responses:
        '202':
          description: Generation queued successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerationResponse'
              example:
                success: true
                generationId: "12345"
                status: "queued"
                model: "gpt-4-vision"
                creditCost: 30
                numberOfImages: 2
                estimatedTime: "60-180 seconds"
                message: "GPT-4 Vision generation has been queued. Use /api/images/status/:id to check progress."
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                missing-prompt:
                  summary: Missing prompt
                  value:
                    error: "Prompt is required and must be a string"
                missing-api-key:
                  summary: Missing OpenAI API key
                  value:
                    error: "OpenAI API key is required for GPT-4 Vision"
                invalid-quality:
                  summary: Invalid quality parameter
                  value:
                    error: "Invalid quality. Must be one of: low, medium, high, auto"
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "Invalid or expired JWT token"
        '402':
          description: Insufficient credits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsufficientCreditsError'
              example:
                error: "Insufficient credits"
                required: 30
                available: 10
                message: "GPT-4 Vision generation requires 30 credits (15 per image). You have 10 credits."
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "Rate limit exceeded. Maximum 10 requests per minute."
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "GPT-4 Vision generation failed"
                message: "Internal server error occurred"

  /status/{generationId}:
    get:
      summary: Check generation status
      description: |
        Check the status of a GPT-4 Vision generation job.

        Possible statuses:
        - `queued`: Job is waiting to be processed
        - `processing`: Job is currently being generated
        - `completed`: Generation finished successfully
        - `failed`: Generation failed with error

        For completed generations, the response includes download URLs for all generated images.
      operationId: getGenerationStatus
      tags:
        - Generation Status
      parameters:
        - name: generationId
          in: path
          required: true
          description: Unique identifier for the generation job
          schema:
            type: string
          example: "12345"
      responses:
        '200':
          description: Status retrieved successfully
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/StatusCompleted'
                  - $ref: '#/components/schemas/StatusProcessing'
                  - $ref: '#/components/schemas/StatusFailed'
              examples:
                completed:
                  summary: Generation completed
                  value:
                    success: true
                    status: "completed"
                    generationId: "12345"
                    model: "gpt-4-vision"
                    prompt: "Add the floral pattern to the vase"
                    creditCost: 30
                    results:
                      - url: "https://replicate.delivery/.../output_0.png"
                        filename: "generated_12345_0.png"
                      - url: "https://replicate.delivery/.../output_1.png"
                        filename: "generated_12345_1.png"
                    createdAt: "2024-10-08T14:30:00Z"
                    completedAt: "2024-10-08T14:32:15Z"
                processing:
                  summary: Generation in progress
                  value:
                    success: true
                    status: "processing"
                    generationId: "12345"
                    estimatedCompletion: "2024-10-08T14:32:00Z"
                    progress:
                      stage: "generating"
                      percentage: 65
                failed:
                  summary: Generation failed
                  value:
                    success: false
                    status: "failed"
                    generationId: "12345"
                    error: "OpenAI API key is invalid or has insufficient quota"
        '404':
          description: Generation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "Generation not found"
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "Invalid or expired JWT token"

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from authentication endpoint

  schemas:
    GPTVisionRequest:
      type: object
      required:
        - prompt
        - openaiApiKey
      properties:
        prompt:
          type: string
          description: Text description of the desired image
          minLength: 1
          maxLength: 1000
          example: "Add the floral pattern to the vase"
        inputImages:
          type: array
          description: Array of input images for style transfer or reference
          maxItems: 10
          items:
            $ref: '#/components/schemas/InputImage'
        openaiApiKey:
          type: string
          format: password
          description: Your OpenAI API key for GPT-4 Vision access
          pattern: '^sk-[a-zA-Z0-9]{32,}$'
          example: "sk-your-openai-api-key"
        options:
          $ref: '#/components/schemas/GenerationOptions'

    InputImage:
      type: object
      required:
        - url
      properties:
        url:
          type: string
          format: uri
          description: Image URL (HTTPS) or base64 data URL
          example: "https://example.com/image.jpg"

    GenerationOptions:
      type: object
      properties:
        quality:
          type: string
          enum: [low, medium, high, auto]
          default: auto
          description: Image generation quality level
        aspectRatio:
          type: string
          enum: ["1:1", "3:2", "2:3"]
          default: "1:1"
          description: Output image aspect ratio
        background:
          type: string
          enum: [auto, transparent, opaque]
          default: auto
          description: Background handling
        moderation:
          type: string
          enum: [auto, low]
          default: auto
          description: Content moderation level
        outputFormat:
          type: string
          enum: [png, jpeg, webp]
          default: webp
          description: Output image format
        inputFidelity:
          type: string
          enum: [low, high]
          default: low
          description: How closely to match input image style
        numberOfImages:
          type: integer
          minimum: 1
          maximum: 10
          default: 1
          description: Number of images to generate
        outputCompression:
          type: integer
          minimum: 0
          maximum: 100
          default: 90
          description: Output compression level (0-100%)
        userId:
          type: string
          description: Optional user ID for monitoring and abuse detection
          example: "user123"

    GenerationResponse:
      type: object
      required:
        - success
        - generationId
        - status
        - model
        - creditCost
        - numberOfImages
        - estimatedTime
        - message
      properties:
        success:
          type: boolean
          example: true
        generationId:
          type: string
          description: Unique identifier for tracking this generation
          example: "12345"
        status:
          type: string
          enum: [queued]
          example: "queued"
        model:
          type: string
          enum: [gpt-4-vision]
          example: "gpt-4-vision"
        creditCost:
          type: integer
          description: Total credits deducted for this generation
          example: 30
        numberOfImages:
          type: integer
          description: Number of images that will be generated
          example: 2
        estimatedTime:
          type: string
          description: Estimated completion time range
          example: "60-180 seconds"
        message:
          type: string
          description: Instructions for checking generation status
          example: "GPT-4 Vision generation has been queued. Use /api/images/status/:id to check progress."

    StatusCompleted:
      type: object
      required:
        - success
        - status
        - generationId
        - model
        - prompt
        - creditCost
        - results
        - createdAt
        - completedAt
      properties:
        success:
          type: boolean
          example: true
        status:
          type: string
          enum: [completed]
          example: "completed"
        generationId:
          type: string
          example: "12345"
        model:
          type: string
          example: "gpt-4-vision"
        prompt:
          type: string
          example: "Add the floral pattern to the vase"
        creditCost:
          type: integer
          example: 30
        results:
          type: array
          items:
            $ref: '#/components/schemas/GeneratedImage'
        createdAt:
          type: string
          format: date-time
          example: "2024-10-08T14:30:00Z"
        completedAt:
          type: string
          format: date-time
          example: "2024-10-08T14:32:15Z"

    StatusProcessing:
      type: object
      required:
        - success
        - status
        - generationId
      properties:
        success:
          type: boolean
          example: true
        status:
          type: string
          enum: [processing, queued]
          example: "processing"
        generationId:
          type: string
          example: "12345"
        estimatedCompletion:
          type: string
          format: date-time
          description: Estimated completion timestamp
          example: "2024-10-08T14:32:00Z"
        progress:
          type: object
          properties:
            stage:
              type: string
              description: Current processing stage
              example: "generating"
            percentage:
              type: integer
              minimum: 0
              maximum: 100
              description: Completion percentage
              example: 65

    StatusFailed:
      type: object
      required:
        - success
        - status
        - generationId
        - error
      properties:
        success:
          type: boolean
          example: false
        status:
          type: string
          enum: [failed]
          example: "failed"
        generationId:
          type: string
          example: "12345"
        error:
          type: string
          description: Error message describing the failure
          example: "OpenAI API key is invalid or has insufficient quota"

    GeneratedImage:
      type: object
      required:
        - url
        - filename
      properties:
        url:
          type: string
          format: uri
          description: Download URL for the generated image
          example: "https://replicate.delivery/.../output_0.png"
        filename:
          type: string
          description: Suggested filename for the image
          example: "generated_12345_0.png"

    ErrorResponse:
      type: object
      required:
        - error
      properties:
        error:
          type: string
          description: Error type or short description
          example: "Invalid request parameters"
        message:
          type: string
          description: Detailed error message
          example: "The 'prompt' field is required and must be a non-empty string"

    InsufficientCreditsError:
      type: object
      required:
        - error
        - required
        - available
        - message
      properties:
        error:
          type: string
          example: "Insufficient credits"
        required:
          type: integer
          description: Credits needed for this operation
          example: 30
        available:
          type: integer
          description: Credits currently available
          example: 10
        message:
          type: string
          description: Detailed explanation of credit requirements
          example: "GPT-4 Vision generation requires 30 credits (15 per image). You have 10 credits."

tags:
  - name: GPT-4 Vision
    description: High-quality image generation using OpenAI's GPT-4 Vision model
  - name: Generation Status
    description: Check the status and results of image generation jobs