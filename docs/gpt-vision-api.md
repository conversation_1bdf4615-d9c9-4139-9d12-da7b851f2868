# GPT-4 Vision API Documentation

## Overview

The GPT-4 Vision API integration allows users to generate high-quality images using OpenAI's GPT-4 Vision model via Replicate. This premium service offers advanced image generation capabilities with multiple input images, sophisticated style controls, and superior output quality compared to standard models.

## Base URL

```
https://api.saia.im/api/images
```

## Authentication

All endpoints require authentication via JWT Bearer token:

```http
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### Generate Images with GPT-4 Vision

**POST** `/gpt-vision`

Generate high-quality images using GPT-4 Vision model with optional input images and advanced controls.

#### Request Body

```json
{
  "prompt": "string (required) - Text description of the desired image",
  "inputImages": [
    {
      "url": "string - Image URL or base64 data URL"
    }
  ],
  "openaiApiKey": "string (required) - Your OpenAI API key",
  "options": {
    "quality": "string - low|medium|high|auto (default: auto)",
    "aspectRatio": "string - 1:1|3:2|2:3 (default: 1:1)",
    "background": "string - auto|transparent|opaque (default: auto)",
    "moderation": "string - auto|low (default: auto)",
    "outputFormat": "string - png|jpeg|webp (default: webp)",
    "inputFidelity": "string - low|high (default: low)",
    "numberOfImages": "number - 1-10 (default: 1)",
    "outputCompression": "number - 0-100 (default: 90)",
    "userId": "string - Optional user ID for monitoring"
  }
}
```

#### Request Example

```json
{
  "prompt": "Add the floral pattern to the vase",
  "inputImages": [
    {
      "url": "https://example.com/vase.jpg"
    },
    {
      "url": "https://example.com/pattern.jpg"
    }
  ],
  "openaiApiKey": "sk-your-openai-api-key",
  "options": {
    "quality": "high",
    "aspectRatio": "1:1",
    "numberOfImages": 2,
    "outputFormat": "png"
  }
}
```

#### Response

**Success (202 Accepted)**

```json
{
  "success": true,
  "generationId": "12345",
  "status": "queued",
  "model": "gpt-4-vision",
  "creditCost": 30,
  "numberOfImages": 2,
  "estimatedTime": "60-180 seconds",
  "message": "GPT-4 Vision generation has been queued. Use /api/images/status/:id to check progress."
}
```

**Error (400 Bad Request)**

```json
{
  "error": "Invalid aspectRatio. Must be one of: 1:1, 3:2, 2:3"
}
```

**Error (402 Payment Required)**

```json
{
  "error": "Insufficient credits",
  "required": 30,
  "available": 10,
  "message": "GPT-4 Vision generation requires 30 credits (15 per image). You have 10 credits."
}
```

### Check Generation Status

**GET** `/status/:generationId`

Check the status of a GPT-4 Vision generation job.

#### Response

**Success (200 OK)**

```json
{
  "success": true,
  "status": "completed",
  "generationId": "12345",
  "model": "gpt-4-vision",
  "prompt": "Add the floral pattern to the vase",
  "creditCost": 30,
  "results": [
    {
      "url": "https://replicate.delivery/.../output_0.png",
      "filename": "generated_12345_0.png"
    },
    {
      "url": "https://replicate.delivery/.../output_1.png",
      "filename": "generated_12345_1.png"
    }
  ],
  "createdAt": "2024-10-08T14:30:00Z",
  "completedAt": "2024-10-08T14:32:15Z"
}
```

**Processing (200 OK)**

```json
{
  "success": true,
  "status": "processing",
  "generationId": "12345",
  "estimatedCompletion": "2024-10-08T14:32:00Z",
  "progress": {
    "stage": "generating",
    "percentage": 65
  }
}
```

**Error (200 OK)**

```json
{
  "success": false,
  "status": "failed",
  "generationId": "12345",
  "error": "OpenAI API key is invalid or has insufficient quota"
}
```

## Parameters Reference

### Quality Options

- **low**: Faster generation, lower quality
- **medium**: Balanced speed and quality
- **high**: Slower generation, highest quality
- **auto**: Automatically selects best quality for the prompt

### Aspect Ratios

- **1:1**: Square format (1024x1024)
- **3:2**: Landscape format (1536x1024)
- **2:3**: Portrait format (1024x1536)

### Background Options

- **auto**: Automatically determines background
- **transparent**: Transparent background (PNG only)
- **opaque**: Solid background

### Input Fidelity

- **low**: More creative interpretation of input images
- **high**: Closer adherence to input image style and features

### Output Formats

- **webp**: Best compression, smaller file sizes
- **png**: Lossless, supports transparency
- **jpeg**: Good compression, no transparency

## Credit System

GPT-4 Vision is a premium service with higher credit costs:

- **Base cost**: 15 credits per image
- **Multiple images**: Cost multiplied by number of images
- **High quality**: Same cost (quality is included)

### Credit Calculation Examples

- 1 image: 15 credits
- 3 images: 45 credits
- 5 images (max recommended): 75 credits

## Input Images

### Supported Formats

- **URLs**: Direct HTTPS URLs to images
- **Base64**: Data URLs with base64-encoded images
- **Formats**: JPEG, PNG, WebP, GIF

### Multiple Images

When providing multiple input images:

1. **First image**: Primary reference for style/content
2. **Additional images**: Style references, patterns, or elements to incorporate
3. **Maximum**: 10 input images per request
4. **Processing**: Images are automatically uploaded to cloud storage if needed

### Image Requirements

- **Size**: Up to 20MB per image
- **Dimensions**: Up to 4096x4096 pixels
- **Total**: Up to 200MB for all input images combined

## Error Handling

### Common Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| 400 | Invalid parameters | Check parameter values against documentation |
| 401 | Authentication failed | Verify JWT token is valid |
| 402 | Insufficient credits | Purchase more credits or reduce number of images |
| 429 | Rate limit exceeded | Wait before making another request |
| 500 | Server error | Retry request or contact support |

### OpenAI-Specific Errors

| Error | Description | Solution |
|-------|-------------|----------|
| Invalid API key | OpenAI key is incorrect | Verify your OpenAI API key |
| Quota exceeded | OpenAI usage limit reached | Check OpenAI billing and limits |
| Content policy | Prompt violates policies | Modify prompt to comply with policies |

## Rate Limits

- **Per user**: 10 requests per minute
- **Concurrent**: 3 active generations per user
- **Queue**: Up to 50 pending jobs per user

## Best Practices

### Prompt Writing

1. **Be specific**: Detailed descriptions yield better results
2. **Use clear language**: Avoid ambiguous terms
3. **Specify style**: Include artistic style or technique preferences
4. **Mention composition**: Describe layout, lighting, perspective

### Input Images

1. **High quality**: Use high-resolution, clear images
2. **Relevant content**: Ensure images relate to your prompt
3. **Consistent style**: Use images with similar artistic styles
4. **Proper order**: Place most important reference image first

### Performance Optimization

1. **Batch requests**: Generate multiple images in one request when possible
2. **Appropriate quality**: Use "auto" quality for most cases
3. **Efficient formats**: Use WebP for smaller file sizes
4. **Monitor usage**: Track credit consumption for cost management

## Example Use Cases

### Style Transfer

```json
{
  "prompt": "Transform this photo into a Van Gogh painting style",
  "inputImages": [
    {"url": "https://example.com/photo.jpg"}
  ],
  "options": {
    "quality": "high",
    "inputFidelity": "high"
  }
}
```

### Object Combination

```json
{
  "prompt": "Combine these elements into a single artistic composition",
  "inputImages": [
    {"url": "https://example.com/object1.jpg"},
    {"url": "https://example.com/object2.jpg"}
  ],
  "options": {
    "aspectRatio": "3:2",
    "background": "transparent"
  }
}
```

### Pattern Application

```json
{
  "prompt": "Apply this pattern to the fabric in the image",
  "inputImages": [
    {"url": "https://example.com/fabric.jpg"},
    {"url": "https://example.com/pattern.jpg"}
  ],
  "options": {
    "inputFidelity": "high",
    "outputFormat": "png"
  }
}
```

## Integration Examples

### JavaScript/Node.js

```javascript
const axios = require('axios');

async function generateWithGPTVision(prompt, inputImages, openaiApiKey) {
  try {
    const response = await axios.post('https://api.saia.im/api/images/gpt-vision', {
      prompt,
      inputImages,
      openaiApiKey,
      options: {
        quality: 'high',
        numberOfImages: 1
      }
    }, {
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json'
      }
    });

    return response.data.generationId;
  } catch (error) {
    console.error('Generation failed:', error.response?.data);
    throw error;
  }
}

async function checkStatus(generationId) {
  const response = await axios.get(`https://api.saia.im/api/images/status/${generationId}`, {
    headers: {
      'Authorization': `Bearer ${jwtToken}`
    }
  });

  return response.data;
}
```

### Python

```python
import requests
import time

def generate_with_gpt_vision(prompt, input_images, openai_api_key, jwt_token):
    response = requests.post(
        'https://api.saia.im/api/images/gpt-vision',
        json={
            'prompt': prompt,
            'inputImages': input_images,
            'openaiApiKey': openai_api_key,
            'options': {
                'quality': 'high',
                'numberOfImages': 1
            }
        },
        headers={
            'Authorization': f'Bearer {jwt_token}',
            'Content-Type': 'application/json'
        }
    )

    response.raise_for_status()
    return response.json()['generationId']

def wait_for_completion(generation_id, jwt_token):
    while True:
        response = requests.get(
            f'https://api.saia.im/api/images/status/{generation_id}',
            headers={'Authorization': f'Bearer {jwt_token}'}
        )

        data = response.json()
        if data['status'] == 'completed':
            return data['results']
        elif data['status'] == 'failed':
            raise Exception(f"Generation failed: {data.get('error')}")

        time.sleep(5)  # Wait 5 seconds before checking again
```

## Support

For technical support or questions about the GPT-4 Vision API:

- **Documentation**: Check this documentation first
- **Status**: Monitor system status for service issues
- **Contact**: Email support for specific integration questions

---

*Last updated: October 2024*