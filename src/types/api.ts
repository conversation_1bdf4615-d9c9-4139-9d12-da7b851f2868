// User Types
export interface User {
  id: number;
  email: string;
  name: string;
  avatar?: string;
  credits: number;
  role: 'user' | 'admin';
  phone?: string;
  phoneVerified: boolean;
  createdAt: string;
}

export interface AuthResponse {
  success: boolean;
  token: string;
  user: User;
}

// Image Generation Types
export interface GenerationRequest {
  prompt: string;
  images?: Array<{ url: string }>;
  options?: {
    aspectRatio?: '1:1' | '3:2' | '2:3';
    mask?: string;
  };
}

export interface GPTVisionRequest {
  prompt: string;
  inputImages?: Array<{ url: string }>;
  openaiApiKey: string;
  options?: {
    quality?: 'low' | 'medium' | 'high' | 'auto';
    aspectRatio?: '1:1' | '3:2' | '2:3';
    background?: 'auto' | 'transparent' | 'opaque';
    outputFormat?: 'png' | 'jpeg' | 'webp';
    numberOfImages?: number;
    inputFidelity?: 'low' | 'high';
  };
}

export interface GenerationResponse {
  success: boolean;
  generationId: string;
  status: 'queued';
  model: string;
  creditCost: number;
  numberOfImages?: number;
  estimatedTime: string;
  message: string;
}

export interface GenerationResult {
  url: string;
  filename: string;
}

export interface GenerationStatus {
  success: boolean;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  generationId: string;
  model?: string;
  prompt?: string;
  creditCost?: number;
  results?: GenerationResult[];
  progress?: {
    stage: string;
    percentage: number;
  };
  error?: string;
  createdAt?: string;
  completedAt?: string;
  estimatedCompletion?: string;
}

// Credit Types
export interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  currency: string;
  description: string;
  popular: boolean;
  bonus: number;
}

export interface PurchaseResponse {
  success: boolean;
  orderId: number;
  package: CreditPackage;
  paymentUrl: string;
  expiresAt: string;
}

export interface PaymentStatus {
  success: boolean;
  orderId: number;
  status: 'pending' | 'completed' | 'failed';
  package: CreditPackage;
  creditsAdded?: number;
  completedAt?: string;
}

// Subscription Types
export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  credits: number;
  features: string[];
}

export interface Subscription {
  id: string;
  planId: string;
  status: 'active' | 'canceled' | 'past_due';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
}

export interface SubscriptionUsage {
  creditsUsed: number;
  creditsTotal: number;
  resetDate: string;
}

// Error Types
export interface APIError {
  error: string;
  message?: string;
  code?: string;
}

export interface InsufficientCreditsError extends APIError {
  required: number;
  available: number;
}
