'use client';

import { useState } from 'react';
import { useImageGeneration } from '@/hooks/useImageGeneration';
import { useCredits } from '@/hooks/useCredits';

export default function ImageGenerator() {
  const [prompt, setPrompt] = useState('');
  const [useGPTVision, setUseGPTVision] = useState(false);
  const [openaiApiKey, setOpenaiApiKey] = useState('');
  const { generateImage, generateWithGPTVision, loading, status } = useImageGeneration();
  const { balance } = useCredits();

  const handleGenerate = async () => {
    const requiredCredits = useGPTVision ? 15 : 3;

    if (balance < requiredCredits) {
      alert(`Не хватает кредитов. Нужно: ${requiredCredits}, доступно: ${balance}`);
      return;
    }

    if (useGPTVision) {
      if (!openaiApiKey) {
        alert('Для GPT-4 Vision требуется OpenAI API Key');
        return;
      }

      await generateWithGPTVision({
        prompt,
        openaiApiKey,
        options: {
          quality: 'high',
          numberOfImages: 1,
        },
      });
    } else {
      await generateImage({
        prompt,
        options: {
          aspectRatio: '1:1',
        },
      });
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm">Prompt</label>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="w-full p-2 border rounded"
          rows={3}
          placeholder="A beautiful sunset over mountains"
        />
      </div>

      <div className="flex items-center space-x-2">
        <input
          id="gptvision"
          type="checkbox"
          checked={useGPTVision}
          onChange={(e) => setUseGPTVision(e.target.checked)}
        />
        <label htmlFor="gptvision">Использовать GPT-4 Vision (15 кредитов)</label>
      </div>

      {useGPTVision && (
        <div>
          <label className="block text-sm">OpenAI API Key</label>
          <input
            type="password"
            value={openaiApiKey}
            onChange={(e) => setOpenaiApiKey(e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="sk-..."
          />
        </div>
      )}

      <div className="text-sm text-gray-600">Доступно кредитов: {balance}</div>

      <button
        onClick={handleGenerate}
        disabled={loading || !prompt}
        className="w-full bg-blue-600 text-white p-2 rounded disabled:opacity-50"
      >
        {loading ? 'Генерация...' : `Сгенерировать (${useGPTVision ? 15 : 3} кредитов)`}
      </button>

      {status && (
        <div className="mt-4 space-y-2">
          <div>Статус: {status.status}</div>
          {status.status === 'processing' && status.progress && (
            <div>Прогресс: {status.progress.percentage}%</div>
          )}
          {status.status === 'completed' && status.results && (
            <div className="mt-2 space-y-2">
              {status.results.map((result, index) => (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  key={index}
                  src={result.url}
                  alt="Generated"
                  className="max-w-full"
                />
              ))}
            </div>
          )}
          {status.status === 'failed' && (
            <div className="text-red-500">Ошибка: {status.error}</div>
          )}
        </div>
      )}
    </div>
  );
}
