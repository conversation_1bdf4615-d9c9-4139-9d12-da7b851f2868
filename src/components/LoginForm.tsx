'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [sent, setSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { sendMagicLink } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      await sendMagicLink(email);
      setSent(true);
    } catch (err: any) {
      setError(err?.message ?? 'Failed to send magic link');
    }
  };

  if (sent) {
    return (
      <div className="text-center space-y-2">
        <h2 className="text-xl font-semibold">Проверьте почту</h2>
        <p>Мы отправили магическую ссылку на {email}</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-sm w-full">
      <div className="space-y-1">
        <label className="block text-sm">Email</label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          placeholder="<EMAIL>"
          className="w-full p-2 border rounded"
        />
      </div>
      {error && <div className="text-red-500 text-sm">{error}</div>}
      <button
        type="submit"
        className="w-full bg-blue-600 text-white p-2 rounded"
      >
        Отправить магическую ссылку
      </button>
    </form>
  );
}
