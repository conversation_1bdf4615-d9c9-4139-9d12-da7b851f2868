import axios from 'axios';

const RAW_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
// Normalize: remove trailing slashes and a trailing '/api' so that client calls like '/api/...'
// do not result in double '/api/api/...'
const normalized = RAW_BASE.replace(/\/+$/, '');
const base = normalized.endsWith('/api') ? normalized.slice(0, -4) : normalized;

const api = axios.create({
  baseURL: base,
  timeout: 30000,
});

api.interceptors.request.use((config) => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers = config.headers ?? {};
      (config.headers as any).Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error?.response?.status === 401 && typeof window !== 'undefined') {
      localStorage.removeItem('auth-token');
      // Redirect to login when unauthorized
      try {
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      } catch {}
    }
    return Promise.reject(error);
  }
);

export default api;
