'use client';

import { useEffect, useState } from 'react';
import api from '@/lib/api';
import type { CreditPackage } from '@/types/api';

export function useCredits() {
  const [balance, setBalance] = useState(0);
  const [packages, setPackages] = useState<CreditPackage[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    void loadBalance();
    void loadPackages();
  }, []);

  const loadBalance = async () => {
    try {
      const response = await api.get('/api/user/balance');
      setBalance(response.data.balance as number);
    } catch (error) {
      console.error('Failed to load balance:', error);
    }
  };

  const loadPackages = async () => {
    try {
      const response = await api.get('/api/credits/packages');
      setPackages(response.data.packages as CreditPackage[]);
    } catch (error) {
      console.error('Failed to load packages:', error);
    } finally {
      setLoading(false);
    }
  };

  const purchaseCredits = async (packageId: string) => {
    const response = await api.post('/api/credits/buy', { packageId });
    return response.data as {
      success: boolean;
      orderId: number;
      paymentUrl: string;
    };
  };

  const checkPaymentStatus = async (orderId: string) => {
    const response = await api.get(`/api/credits/payment/status/${orderId}`);
    return response.data;
  };

  return {
    balance,
    packages,
    loading,
    loadBalance,
    purchaseCredits,
    checkPaymentStatus,
  } as const;
}
