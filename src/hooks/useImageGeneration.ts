'use client';

import { useState } from 'react';
import api from '@/lib/api';
import type {
  GenerationRequest,
  GenerationStatus,
  GPTVisionRequest,
} from '@/types/api';

export function useImageGeneration() {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<GenerationStatus | null>(null);

  const generateImage = async (request: GenerationRequest): Promise<string> => {
    setLoading(true);
    try {
      const response = await api.post('/api/images/generate', request);
      const generationId = response.data.generationId as string;
      void pollStatus(generationId);
      return generationId;
    } finally {
      setLoading(false);
    }
  };

  const generateWithGPTVision = async (request: GPTVisionRequest): Promise<string> => {
    setLoading(true);
    try {
      const response = await api.post('/api/images/gpt-vision', request);
      const generationId = response.data.generationId as string;
      void pollStatus(generationId);
      return generationId;
    } finally {
      setLoading(false);
    }
  };

  const pollStatus = async (generationId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await api.get(`/api/images/status/${generationId}`);
        const statusData = response.data as GenerationStatus;
        setStatus(statusData);
        if (
          statusData.status === 'completed' ||
          statusData.status === 'failed'
        ) {
          clearInterval(interval);
        }
      } catch (error) {
        console.error('Status polling error:', error);
        clearInterval(interval);
      }
    }, 3000);
  };

  const cancelGeneration = async (generationId: string) => {
    await api.delete(`/api/images/cancel/${generationId}`);
    setStatus(null);
  };

  return {
    loading,
    status,
    generateImage,
    generateWithGPTVision,
    cancelGeneration,
    clearStatus: () => setStatus(null),
  } as const;
}
