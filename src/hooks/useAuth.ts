'use client';

import { useEffect, useState } from 'react';
import api from '@/lib/api';
import type { User } from '@/types/api';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    void checkAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkAuth = async () => {
    try {
      if (typeof window === 'undefined') return;
      const token = localStorage.getItem('auth-token');
      if (!token) {
        setLoading(false);
        return;
      }
      const response = await api.get('/api/auth/me');
      setUser(response.data.user);
    } catch {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth-token');
      }
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const sendMagicLink = async (email: string) => {
    const response = await api.post('/api/auth/send-magic-link', {
      email,
      language: 'ru',
    });
    return response.data as { success: boolean; message: string; expires?: string };
  };

  const verifyMagicLink = async (token: string) => {
    const response = await api.post('/api/auth/verify-magic-link', { token });
    if (response.data?.success && typeof window !== 'undefined') {
      localStorage.setItem('auth-token', response.data.token);
      setUser(response.data.user);
    }
    return response.data as { success: boolean; token?: string; user?: User };
  };

  const logout = async () => {
    try {
      await api.post('/api/auth/logout');
    } finally {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth-token');
      }
      setUser(null);
    }
  };

  return {
    user,
    loading,
    isAuthenticated: !!user,
    checkAuth,
    sendMagicLink,
    verifyMagicLink,
    logout,
  } as const;
}
